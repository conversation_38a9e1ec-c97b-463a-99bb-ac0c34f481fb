import { useStaticQuery, graphql } from 'gatsby';

export const useAllBlog = () => {
    const AllPosts = useStaticQuery(
        graphql`
            query GET_All_POSTS {
                allWordpressPost {
                    nodes {
                        featured_media {
                            localFile {
                                childImageSharp {
                                    fluid {
                                        ...GatsbyImageSharpFluid
                                    }
                                    fixed {
                                        ...GatsbyImageSharpFixed
                                    }
                                }
                            }
                        }
                        categories {
                            name
                        }
                        date
                        excerpt
                        slug
                        title
                        id
                        wordpress_id
                    }
                }
            }
        `
    );
    return AllPosts;
};
