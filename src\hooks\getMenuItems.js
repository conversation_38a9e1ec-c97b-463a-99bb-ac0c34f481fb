import { useStaticQuery, graphql } from 'gatsby';

export const useMenuitems = () => {
    const AllMenuItems = useStaticQuery(
        graphql`
            query GET_ALL_MENU_ITEMS {
                allWordpressWpApiMenusMenusItems(
                    filter: { slug: { eq: "main-menu" } }
                ) {
                    edges {
                        node {
                            slug
                            name
                            items {
                                title
                                url
                                wordpress_id
                                type
                                wordpress_children {
                                    title
                                    wordpress_id
                                    url
                                    type
                                }
                            }
                        }
                    }
                }
            }
        `
    );
    return AllMenuItems.allWordpressWpApiMenusMenusItems.edges[0].node.items;
};
