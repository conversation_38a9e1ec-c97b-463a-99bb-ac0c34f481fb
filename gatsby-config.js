module.exports = {
    siteMetadata: {
        title: `Wrts Milwaukee`,
        description: ``,
        author: `@gatsbyjs`,
    },
    plugins: [
        `gatsby-plugin-react-helmet`,
        {
            resolve: `gatsby-source-filesystem`,
            options: {
                name: `images`,
                path: `${__dirname}/src/images`,
            },
        },
        `gatsby-transformer-sharp`,
        {
            resolve: `gatsby-plugin-sharp`,
            options: {
                defaultQuality: 90,
            },
        },
        {
            resolve: `gatsby-plugin-manifest`,
            options: {
                name: `Wrts Milwaukee`,
                short_name: `Wrts Milwaukee`,
                start_url: `/`,
                background_color: `#146fb8`,
                theme_color: `#146fb8`,
                display: `minimal-ui`,
                icon: `src/images/fav.png`, // This path is relative to the root of the site.
            },
        },
        'gatsby-plugin-sass',
        {
            resolve: 'gatsby-source-wordpress',
            options: {
                // The base url to your WP site.
                baseUrl: 'werockthespectrummilwaukee.wrtsfranchise.com',
                // WP.com sites set to true, WP.org set to false
                hostingWPCOM: false,
                // The protocol. This can be http or https.
                protocol: 'https',
                // Use 'Advanced Custom Fields' Wordpress plugin
                useACF: true,
                auth: {},
                // Set to true to debug endpoints on 'gatsby build'
                verboseOutput: false,
                includedRoutes: [
                    '**/pages',
                    '**/posts',
                    '**/categories',
                    '**/events_post_type',
                    '**/resources_post_type',
                    '**/media',
                    '**/options/**',
                    '**/wp-api-menus/v2/**',
                ],
                excludedRoutes: [
                    '**/wp/v2/users/me/**',
                    '**/wp/v2/settings/**',
                ],
            },
        },
        // {
        //     resolve: `gatsby-plugin-google-analytics`,
        //     options: {
        //         trackingId: 'UA-167875719-1',
        //         cookieDomain: 'werockthespectrumkidsgym.com',
        //     },
        // },
        {
            // Removes unused css rules
            resolve: 'gatsby-plugin-purgecss',
            options: {
                // Activates purging in gatsby develop
                develop: true,
                // Purge only the main css file
                purgeOnly: ['style/app.scss'],
            },
        }, // must be after other CSS plugins
        {
            resolve: `gatsby-plugin-offline`,
            options: {
                workboxConfig: {
                    skipWaiting: true, // Activate the new service worker immediately
                    clientsClaim: true, // Control all pages as soon as the service worker activates
                    runtimeCaching: [
                        {
                            urlPattern: /^https?.*/, // Match all requests
                            handler: `NetworkFirst`, // Always check the network first
                            options: {
                                cacheName: `dynamic-content`,
                                expiration: {
                                    maxEntries: 50, // Limit cache size
                                },
                            },
                        },
                    ],
                },
            },
        },
        'gatsby-plugin-netlify',
    ],
};
