import React, { Component } from 'react';
import Slider from 'react-slick';
import '../../../node_modules/slick-carousel/slick/slick.css';
import '../../../node_modules/slick-carousel/slick/slick-theme.css';
import wrow0 from '../../images/wrow0.jpg';
import wrow1 from '../../images/wrow1.jpg';
import wrow2 from '../../images/wrow2.jpg';
import wrow3 from '../../images/wrow3.jpg';

const settings = {
    dots: true,
    infinite: true,
    speed: 500,
    arrows: false,
    slidesToShow: 1,
    slidesToScroll: 1,
};

const dotsSettings = {
    dots: false,
    infinite: true,
    speed: 500,
    arrows: false,
    slidesToShow: 4,
    slidesToScroll: 1,
    focusOnSelect: true,
};

class WeRockWheelsSlider extends Component {
    constructor(props) {
        super(props);
        this.state = {
            nav1: null,
            nav2: null,
        };
    }

    componentDidMount() {
        this.setState({
            nav1: this.slider1,
            nav2: this.slider2,
        });
    }

    render() {
        const { eqGallery } = this.props;
        console.log(eqGallery);
        return (
            <>
                <Slider
                    asNavFor={this.state.nav2}
                    ref={slider => (this.slider1 = slider)}
                    {...dotsSettings}
                    className="carousel-indicators"
                >
                    {eqGallery.map((image, i) => (
                        <li key={i}></li>
                    ))}
                </Slider>
                <Slider
                    {...settings}
                    asNavFor={this.state.nav1}
                    ref={slider => (this.slider2 = slider)}
                    className="carousel-inner"
                >
                    {eqGallery.map((image, i) => (
                        <div className="item carousel-item" key={i}>
                            <img
                                src={image.localFile.childImageSharp.fluid.src}
                                key={i}
                                alt="wrow0"
                            />
                        </div>
                    ))}
                </Slider>
            </>
        );
    }
}

export default WeRockWheelsSlider;
